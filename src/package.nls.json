{"extension.displayName": "<PERSON><PERSON><PERSON>:AI Coding Assistant-Intelligent partner in development", "extension.description": "<PERSON><PERSON><PERSON>:AI Coding Assistant-your intelligent partner in software development with automatic code generation", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.activitybar.title": "湛卢", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.newTask.title": "New Task", "command.mcpServers.title": "MCP Servers", "command.prompts.title": "Modes", "command.history.title": "History", "command.marketplace.title": "Marketplace", "command.roomoteAgent.title": "Roomote Agent", "command.openInEditor.title": "Open in Editor", "command.settings.title": "Settings", "command.documentation.title": "Documentation", "command.logout.title": "Sign Out", "command.openInNewTab.title": "Open In New Tab", "command.explainCode.title": "Explain Code", "command.fixCode.title": "Fix Code", "command.improveCode.title": "Improve Code", "command.unitTest.title": "Unit Test", "command.codeReview.title": "Code Review", "command.commentCode.title": "Comment Code", "command.addToContext.title": "Add To Context", "command.focusInput.title": "Focus Input Field", "command.setCustomStoragePath.title": "Set Custom Storage Path", "command.importSettings.title": "Import Settings", "command.terminal.addToContext.title": "Add Terminal Content to Context", "command.terminal.fixCommand.title": "Fix This Command", "command.terminal.explainCommand.title": "Explain This Command", "command.terminal.fixCommandInCurrentTask.title": "Fix This Command In Current Task", "command.terminal.explainCommandInCurrentTask.title": "Explain This Command In Current Task", "command.acceptInput.title": "Accept Input/Suggestion", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "Commands that can be auto-executed when 'Always approve execute operations' is enabled", "commands.deniedCommands.description": "Command prefixes that will be automatically denied without asking for approval. In case of conflicts with allowed commands, the longest prefix match takes precedence. Add * to deny all commands.", "commands.commandExecutionTimeout.description": "Maximum time in seconds to wait for command execution to complete before timing out (0 = no timeout, 1-600s, default: 0s)", "commands.commandTimeoutAllowlist.description": "Command prefixes that are excluded from the command execution timeout. Commands matching these prefixes will run without timeout restrictions.", "commands.preventCompletionWithOpenTodos.description": "Prevent task completion when there are incomplete todos in the todo list", "settings.vsCodeLmModelSelector.description": "Settings for VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "The vendor of the language model (e.g. copilot)", "settings.vsCodeLmModelSelector.family.description": "The family of the language model (e.g. gpt-4)", "settings.completion.debounce_time.description": "Code completion trigger delay in milliseconds (ms)", "settings.completion.completion_number.description": "Number of code completion candidates generated", "settings.completion.inlineCompletion_granularity.description": "Preference for completion granularity", "settings.completion.inlineCompletion_granularity.singleRow": "Single Row", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "One-Time Maximization", "settings.completion.inlineCompletion_granularity.balanced": "Balanced", "settings.completion.multiple_line_Completion.description": "Multi-line code completion method", "settings.completion.multiple_line_Completion.autoCompletion": "Auto Completion", "settings.completion.multiple_line_Completion.triggerCompletion": "Trigger Completion", "settings.completion.multiple_line_Completion.autoCompletion.description": "Enter and Ctrl+K (Cmd+K on Mac) can trigger multi-line completion", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Only Ctrl+K (Cmd+K on Mac) triggers multi-line completion, Enter does not", "settings.completion.max_tokens_completion.description": "Max tokens for code completion", "settings.dmt.maxTokens.description": "Max_token for Multimodal Q&A of Graph generated Code", "settings.serverBaseUrl.description": "Base URL for Zhanlu server, defaults to https://api-wuxi-1.cmecloud.cn:8443", "settings.customStoragePath.description": "Custom storage path. Leave empty to use the default location. Supports absolute paths (e.g. 'D:\\RooCodeStorage')", "settings.rooCodeCloudEnabled.description": "Enable Zhanlu Cloud features and account integration", "settings.enableCodeActions.description": "Enable Roo Code quick fixes", "settings.autoImportSettingsPath.description": "Path to a RooCode configuration file to automatically import on extension startup. Supports absolute paths and paths relative to the home directory (e.g. '~/Documents/roo-code-settings.json'). Leave empty to disable auto-import.", "settings.useAgentRules.description": "Enable loading of AGENTS.md files for agent-specific rules (see https://agent-rules.org/)"}